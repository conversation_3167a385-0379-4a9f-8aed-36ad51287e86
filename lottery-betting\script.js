// 游戏数据管理
class LotteryGame {
    constructor() {
        this.data = this.loadData();
        this.selectedNumbers = [[], [], [], []]; // 万位、仟位、佰位、十位
        this.init();
    }

    // 从localStorage加载数据
    loadData() {
        const defaultData = {
            currentPeriod: '',
            currentNumbers: ['-', '-', '-', '-', '-'],
            lotteryHistory: [],
            userStats: {
                currentPoints: 10000,
                totalBets: 0,
                totalWinnings: 0
            },
            bettingRecords: [],
            isDrawn: false,
            lastFetchedPeriod: ''
        };

        const savedData = localStorage.getItem('lotteryGameData');
        return savedData ? { ...defaultData, ...JSON.parse(savedData) } : defaultData;
    }

    // 保存数据到localStorage
    saveData() {
        localStorage.setItem('lotteryGameData', JSON.stringify(this.data));
    }

    // 初始化
    init() {
        this.initTabs();
        this.initNumberSelection();
        this.initEventListeners();
        this.updateDisplay();
    }

    // 初始化标签页
    initTabs() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.dataset.tab;
                
                // 移除所有活动状态
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // 添加活动状态
                button.classList.add('active');
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });
    }

    // 初始化选号区域
    initNumberSelection() {
        const positions = document.querySelectorAll('.number-buttons');
        
        positions.forEach((position, posIndex) => {
            // 生成0-9数字按钮
            for (let i = 0; i <= 9; i++) {
                const button = document.createElement('button');
                button.className = 'number-btn';
                button.textContent = i;
                button.dataset.number = i;
                button.dataset.position = posIndex;
                
                button.addEventListener('click', () => {
                    this.toggleNumber(posIndex, i, button);
                });
                
                position.appendChild(button);
            }
        });
    }

    // 初始化事件监听器
    initEventListeners() {
        // 开奖按钮
        document.getElementById('drawBtn').addEventListener('click', () => {
            this.drawLottery();
        });

        // 采集开奖按钮
        document.getElementById('fetchBtn').addEventListener('click', () => {
            this.fetchLotteryResults();
        });

        // 清空选择按钮
        document.getElementById('clearSelection').addEventListener('click', () => {
            this.clearSelection();
        });

        // 下注按钮
        document.getElementById('placeBet').addEventListener('click', () => {
            this.placeBet();
        });

        // 投注积分输入
        document.getElementById('betPoints').addEventListener('input', () => {
            this.updateBetInfo();
        });

        // 充值按钮
        document.getElementById('rechargeBtn').addEventListener('click', () => {
            this.rechargePoints();
        });

        // 重置积分按钮
        document.getElementById('resetPointsBtn').addEventListener('click', () => {
            this.resetPoints();
        });
    }

    // 切换数字选择
    toggleNumber(position, number, button) {
        const selectedNumbers = this.selectedNumbers[position];
        const numberIndex = selectedNumbers.indexOf(number);

        if (numberIndex > -1) {
            // 取消选择
            selectedNumbers.splice(numberIndex, 1);
            button.classList.remove('selected');
        } else {
            // 选择数字
            selectedNumbers.push(number);
            button.classList.add('selected');
        }

        this.updateSelectedDisplay(position);
        this.updateBetInfo();
    }

    // 更新选中数字显示
    updateSelectedDisplay(position) {
        const selectedDiv = document.getElementById(`selected-${position}`);
        const numbers = this.selectedNumbers[position];
        selectedDiv.textContent = numbers.length > 0 ? numbers.sort().join(', ') : '未选择';
    }

    // 更新下注信息
    updateBetInfo() {
        const selectedPositions = this.selectedNumbers.filter(pos => pos.length > 0);
        const betPointsInput = document.getElementById('betPoints');
        const betPoints = parseInt(betPointsInput.value) || 0;

        let betType = '请选择号码';
        let betCount = 0;
        let betOdds = '-';
        let totalBet = 0;

        if (selectedPositions.length >= 2) {
            if (selectedPositions.length === 2) {
                betType = '二字定';
                betOdds = '95';
            } else if (selectedPositions.length === 3) {
                betType = '三字定';
                betOdds = '950';
            } else if (selectedPositions.length === 4) {
                betType = '四字定';
                betOdds = '9500';
            }

            // 计算注数（各位置选中数字数量的乘积）
            betCount = selectedPositions.reduce((acc, pos) => acc * pos.length, 1);
            totalBet = betCount * betPoints;
        }

        document.getElementById('betType').textContent = betType;
        document.getElementById('betCount').textContent = betCount;
        document.getElementById('betOdds').textContent = betOdds;
        document.getElementById('totalBet').textContent = totalBet;

        // 更新下注按钮状态
        const placeBetBtn = document.getElementById('placeBet');
        placeBetBtn.disabled = selectedPositions.length < 2 || betPoints <= 0 || totalBet > this.data.userStats.currentPoints;
    }

    // 清空选择
    clearSelection() {
        this.selectedNumbers = [[], [], [], []];
        
        // 清除所有选中状态
        document.querySelectorAll('.number-btn.selected').forEach(btn => {
            btn.classList.remove('selected');
        });

        // 更新显示
        for (let i = 0; i < 4; i++) {
            this.updateSelectedDisplay(i);
        }
        this.updateBetInfo();
    }

    // 下注
    placeBet() {
        const selectedPositions = this.selectedNumbers.filter(pos => pos.length > 0);
        if (selectedPositions.length < 2) {
            this.showMessage('请至少选择两个位置的号码', 'error');
            return;
        }

        const betPoints = parseInt(document.getElementById('betPoints').value) || 0;
        const betCount = selectedPositions.reduce((acc, pos) => acc * pos.length, 1);
        const totalBet = betCount * betPoints;

        if (totalBet > this.data.userStats.currentPoints) {
            this.showMessage('积分不足', 'error');
            return;
        }

        // 创建下注记录
        const betRecord = {
            id: Date.now(),
            period: this.data.currentPeriod || '待开奖',
            selectedNumbers: [...this.selectedNumbers],
            betType: selectedPositions.length === 2 ? '二字定' : 
                    selectedPositions.length === 3 ? '三字定' : '四字定',
            betCount: betCount,
            betPoints: betPoints,
            totalBet: totalBet,
            odds: selectedPositions.length === 2 ? 95 : 
                  selectedPositions.length === 3 ? 950 : 9500,
            status: 'pending',
            winnings: 0,
            timestamp: new Date().toLocaleString()
        };

        // 扣除积分
        this.data.userStats.currentPoints -= totalBet;
        this.data.userStats.totalBets += totalBet;

        // 添加下注记录
        this.data.bettingRecords.unshift(betRecord);

        // 清空选择
        this.clearSelection();

        // 保存数据并更新显示
        this.saveData();
        this.updateDisplay();

        this.showMessage(`下注成功！投注${totalBet}积分`, 'success');
    }

    // 开奖
    drawLottery() {
        if (this.data.isDrawn && this.data.currentPeriod) {
            this.showMessage('本期已经开奖', 'warning');
            return;
        }

        // 生成随机开奖号码
        const numbers = [];
        for (let i = 0; i < 5; i++) {
            numbers.push(Math.floor(Math.random() * 10));
        }

        // 生成期号
        const now = new Date();
        const period = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}`;

        this.data.currentPeriod = period;
        this.data.currentNumbers = numbers;
        this.data.isDrawn = true;

        // 添加到历史记录
        this.data.lotteryHistory.unshift({
            period: period,
            numbers: [...numbers],
            timestamp: new Date().toLocaleString()
        });

        // 只保留最近两期历史
        if (this.data.lotteryHistory.length > 2) {
            this.data.lotteryHistory = this.data.lotteryHistory.slice(0, 2);
        }

        // 结算下注
        this.settleBets();

        // 保存数据并更新显示
        this.saveData();
        this.updateDisplay();

        this.showMessage('开奖完成！', 'success');
    }

    // 采集开奖结果
    async fetchLotteryResults() {
        try {
            this.showMessage('正在采集开奖结果...', 'warning');

            // 添加加载状态
            const fetchBtn = document.getElementById('fetchBtn');
            const originalText = fetchBtn.textContent;
            fetchBtn.innerHTML = '<span class="loading"></span> 采集中...';
            fetchBtn.disabled = true;

            const response = await fetch('https://api.api68.com/QuanGuoCai/getLotteryInfo.do?lotCode=10044', {
                method: 'GET',
                mode: 'cors'
            });
            const data = await response.json();

            if (data && data.result && data.result.length > 0) {
                const latestResult = data.result[0];
                const period = latestResult.expect;
                const numbers = latestResult.opencode.split(',').map(num => parseInt(num));

                // 检查是否是最新期号
                if (period === this.data.lastFetchedPeriod) {
                    this.showMessage('已经是最新期号，无需重复采集', 'warning');
                    return;
                }

                this.data.currentPeriod = period;
                this.data.currentNumbers = numbers;
                this.data.isDrawn = true;
                this.data.lastFetchedPeriod = period;

                // 添加到历史记录
                this.data.lotteryHistory.unshift({
                    period: period,
                    numbers: [...numbers],
                    timestamp: new Date().toLocaleString()
                });

                // 只保留最近两期历史
                if (this.data.lotteryHistory.length > 2) {
                    this.data.lotteryHistory = this.data.lotteryHistory.slice(0, 2);
                }

                // 结算下注
                this.settleBets();

                // 保存数据并更新显示
                this.saveData();
                this.updateDisplay();

                this.showMessage('采集开奖结果成功！', 'success');
            } else {
                this.showMessage('采集失败，请稍后重试', 'error');
            }
        } catch (error) {
            console.error('采集开奖结果失败:', error);
            this.showMessage('采集失败，请检查网络连接', 'error');
        } finally {
            // 恢复按钮状态
            const fetchBtn = document.getElementById('fetchBtn');
            fetchBtn.textContent = '采集开奖';
            fetchBtn.disabled = false;
        }
    }

    // 结算下注
    settleBets() {
        const winningNumbers = this.data.currentNumbers.slice(0, 4); // 只取前4位

        this.data.bettingRecords.forEach(record => {
            if (record.status === 'pending') {
                const isWin = this.checkWin(record.selectedNumbers, winningNumbers);
                
                if (isWin) {
                    record.status = 'win';
                    record.winnings = record.totalBet * record.odds;
                    this.data.userStats.currentPoints += record.winnings;
                    this.data.userStats.totalWinnings += record.winnings;
                } else {
                    record.status = 'lose';
                    record.winnings = 0;
                }
            }
        });
    }

    // 检查是否中奖
    checkWin(selectedNumbers, winningNumbers) {
        const selectedPositions = [];
        
        // 找出有选择数字的位置
        selectedNumbers.forEach((numbers, position) => {
            if (numbers.length > 0) {
                selectedPositions.push(position);
            }
        });

        // 检查每个选中位置的数字是否匹配
        return selectedPositions.every(position => {
            return selectedNumbers[position].includes(winningNumbers[position]);
        });
    }

    // 撤销下注
    cancelBet(betId) {
        const recordIndex = this.data.bettingRecords.findIndex(record => record.id === betId);
        
        if (recordIndex > -1) {
            const record = this.data.bettingRecords[recordIndex];
            
            if (record.status === 'pending') {
                // 退还积分
                this.data.userStats.currentPoints += record.totalBet;
                this.data.userStats.totalBets -= record.totalBet;
                
                // 删除记录
                this.data.bettingRecords.splice(recordIndex, 1);
                
                // 保存数据并更新显示
                this.saveData();
                this.updateDisplay();
                
                this.showMessage('撤单成功！', 'success');
            } else {
                this.showMessage('该注单已开奖，无法撤销', 'error');
            }
        }
    }

    // 充值积分
    rechargePoints() {
        const amount = parseInt(document.getElementById('rechargeAmount').value) || 0;
        
        if (amount <= 0) {
            this.showMessage('请输入有效的充值金额', 'error');
            return;
        }

        this.data.userStats.currentPoints += amount;
        this.saveData();
        this.updateDisplay();
        
        this.showMessage(`充值成功！获得${amount}积分`, 'success');
    }

    // 重置积分
    resetPoints() {
        if (confirm('确定要重置积分吗？这将清除所有数据！')) {
            this.data = {
                currentPeriod: '',
                currentNumbers: ['-', '-', '-', '-', '-'],
                lotteryHistory: [],
                userStats: {
                    currentPoints: 10000,
                    totalBets: 0,
                    totalWinnings: 0
                },
                bettingRecords: [],
                isDrawn: false,
                lastFetchedPeriod: ''
            };

            this.saveData();
            this.updateDisplay();

            this.showMessage('数据重置成功！', 'success');
        }
    }

    // 格式化数字显示
    formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }

    // 更新显示
    updateDisplay() {
        this.updateLotteryDisplay();
        this.updateUserStatsDisplay();
        this.updateBettingRecordsDisplay();
    }

    // 更新开奖显示
    updateLotteryDisplay() {
        // 更新当前期号
        document.getElementById('currentPeriod').textContent = this.data.currentPeriod || '等待开奖...';
        
        // 更新开奖号码
        for (let i = 0; i < 5; i++) {
            document.getElementById(`num${i + 1}`).textContent = this.data.currentNumbers[i];
        }

        // 更新开奖按钮状态
        const drawBtn = document.getElementById('drawBtn');
        if (this.data.isDrawn && this.data.currentPeriod) {
            drawBtn.textContent = '已开奖';
            drawBtn.disabled = true;
        } else {
            drawBtn.textContent = '开奖';
            drawBtn.disabled = false;
        }

        // 更新历史记录
        this.updateHistoryDisplay();
    }

    // 更新历史记录显示
    updateHistoryDisplay() {
        const historyList = document.getElementById('historyList');
        historyList.innerHTML = '';

        if (this.data.lotteryHistory.length === 0) {
            historyList.innerHTML = '<p style="text-align: center; color: #666;">暂无开奖历史</p>';
            return;
        }

        this.data.lotteryHistory.forEach(history => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            
            historyItem.innerHTML = `
                <div class="history-period">${history.period}</div>
                <div class="history-numbers">
                    ${history.numbers.map(num => `<span class="history-number">${num}</span>`).join('')}
                </div>
                <div class="history-time">${history.timestamp}</div>
            `;
            
            historyList.appendChild(historyItem);
        });
    }

    // 更新用户统计显示
    updateUserStatsDisplay() {
        document.getElementById('currentPoints').textContent = this.formatNumber(this.data.userStats.currentPoints);
        document.getElementById('userCurrentPoints').textContent = this.formatNumber(this.data.userStats.currentPoints);
        document.getElementById('userTotalBets').textContent = this.formatNumber(this.data.userStats.totalBets);
        document.getElementById('userTotalWinnings').textContent = this.formatNumber(this.data.userStats.totalWinnings);
    }

    // 更新下注记录显示
    updateBettingRecordsDisplay() {
        const recordsList = document.getElementById('recordsList');
        recordsList.innerHTML = '';

        if (this.data.bettingRecords.length === 0) {
            recordsList.innerHTML = '<p style="text-align: center; color: #666;">暂无下注记录</p>';
            return;
        }

        this.data.bettingRecords.forEach(record => {
            const recordItem = document.createElement('div');
            recordItem.className = 'record-item';
            
            const statusText = record.status === 'pending' ? '未开奖' : 
                              record.status === 'win' ? '中奖' : '未中奖';
            const statusClass = record.status;

            const selectedNumbersDisplay = record.selectedNumbers.map((numbers, index) => {
                if (numbers.length > 0) {
                    return `${['万', '仟', '佰', '十'][index]}位: ${numbers.join(',')}`;
                }
                return '';
            }).filter(str => str).join(' | ');

            recordItem.innerHTML = `
                <div class="record-header">
                    <div class="record-period">期号: ${record.period}</div>
                    <div class="record-status ${statusClass}">${statusText}</div>
                </div>
                <div class="record-details">
                    <div class="record-detail">
                        <span>投注类型:</span>
                        <span>${record.betType}</span>
                    </div>
                    <div class="record-detail">
                        <span>投注积分:</span>
                        <span>${record.totalBet}</span>
                    </div>
                    <div class="record-detail">
                        <span>中奖奖金:</span>
                        <span>${record.winnings}</span>
                    </div>
                </div>
                <div class="record-numbers-info">
                    <strong>投注号码:</strong> ${selectedNumbersDisplay}
                </div>
                ${record.status === 'pending' ? `
                    <div class="record-actions">
                        <button class="cancel-button" onclick="game.cancelBet(${record.id})">撤单</button>
                    </div>
                ` : ''}
            `;
            
            recordsList.appendChild(recordItem);
        });
    }

    // 显示消息
    showMessage(text, type = 'success') {
        const message = document.createElement('div');
        message.className = `message ${type}`;
        message.textContent = text;
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            message.remove();
        }, 3000);
    }
}

// 初始化游戏
let game;
document.addEventListener('DOMContentLoaded', () => {
    game = new LotteryGame();
});
