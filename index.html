<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排列五积分下注网站</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎰</text></svg>">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1>🎰 排列五积分下注网站</h1>
            <div class="points-display">
                当前积分: <span id="currentPoints">10000</span>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 第一模块：开奖结果 -->
            <section class="module lottery-results">
                <h2>🎯 开奖结果</h2>
                <div class="draw-section">
                    <div class="current-draw">
                        <div class="period-info">
                            <span>当前期号：</span>
                            <span id="currentPeriod">等待开奖...</span>
                        </div>
                        <div class="draw-numbers">
                            <div class="number-display">
                                <span class="position-label">万位</span>
                                <span class="number" id="num1">-</span>
                            </div>
                            <div class="number-display">
                                <span class="position-label">仟位</span>
                                <span class="number" id="num2">-</span>
                            </div>
                            <div class="number-display">
                                <span class="position-label">佰位</span>
                                <span class="number" id="num3">-</span>
                            </div>
                            <div class="number-display">
                                <span class="position-label">十位</span>
                                <span class="number" id="num4">-</span>
                            </div>
                            <div class="number-display">
                                <span class="position-label">个位</span>
                                <span class="number" id="num5">-</span>
                            </div>
                        </div>
                        <div class="draw-actions">
                            <button id="fetchBtn" class="btn btn-primary">采集开奖</button>
                            <button id="newDrawBtn" class="btn btn-secondary">新开奖</button>
                        </div>
                    </div>
                    
                    <div class="draw-history">
                        <h3>开奖历史</h3>
                        <div id="historyList" class="history-list">
                            <!-- 历史记录将在这里显示 -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- 第二模块：选号区域 -->
            <section class="module number-selection">
                <h2>🎲 选号区域</h2>
                <div class="selection-form">
                    <div class="input-grid">
                        <div class="input-group">
                            <label for="wan">万位</label>
                            <input type="text" id="wan" class="number-input" placeholder="输入0-9数字" maxlength="10">
                            <small class="help-text">可输入多个数字，如：123</small>
                        </div>
                        <div class="input-group">
                            <label for="qian">仟位</label>
                            <input type="text" id="qian" class="number-input" placeholder="输入0-9数字" maxlength="10">
                            <small class="help-text">可输入多个数字，如：456</small>
                        </div>
                        <div class="input-group">
                            <label for="bai">佰位</label>
                            <input type="text" id="bai" class="number-input" placeholder="输入0-9数字" maxlength="10">
                            <small class="help-text">可输入多个数字，如：789</small>
                        </div>
                        <div class="input-group">
                            <label for="shi">十位</label>
                            <input type="text" id="shi" class="number-input" placeholder="输入0-9数字" maxlength="10">
                            <small class="help-text">可输入多个数字，如：012</small>
                        </div>
                    </div>

                    <div class="bet-info">
                        <div class="bet-details">
                            <div class="detail-item">
                                <span>投注类型：</span>
                                <span id="betType">请选择号码</span>
                            </div>
                            <div class="detail-item">
                                <span>注数：</span>
                                <span id="betCount">0</span>
                            </div>
                            <div class="detail-item">
                                <span>赔率：</span>
                                <span id="betOdds">-</span>
                            </div>
                        </div>
                    </div>

                    <div class="bet-amount">
                        <label for="betPoints">投注积分：</label>
                        <input type="number" id="betPoints" min="1" value="100" class="points-input">
                        <span class="total-bet">总投注：<span id="totalBet">0</span></span>
                    </div>

                    <div class="bet-actions">
                        <button id="clearSelection" class="btn btn-secondary">清空选择</button>
                        <button id="placeBet" class="btn btn-success" disabled>下注</button>
                    </div>
                </div>
            </section>

            <!-- 第三模块：用户中心 -->
            <section class="module user-center">
                <h2>👤 用户中心</h2>
                <div class="user-stats">
                    <div class="stat-card">
                        <div class="stat-label">当前积分</div>
                        <div class="stat-value" id="userCurrentPoints">10000</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">总下注</div>
                        <div class="stat-value" id="userTotalBets">0</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">总奖金</div>
                        <div class="stat-value" id="userTotalWinnings">0</div>
                    </div>
                </div>

                <div class="user-actions">
                    <div class="action-group">
                        <label for="rechargeAmount">充值积分：</label>
                        <input type="number" id="rechargeAmount" min="1" value="1000" class="points-input">
                        <button id="rechargeBtn" class="btn btn-primary">充值</button>
                    </div>
                    <div class="action-group">
                        <button id="resetPointsBtn" class="btn btn-danger">重置积分</button>
                    </div>
                </div>
            </section>

            <!-- 第四模块：下注记录 -->
            <section class="module betting-records">
                <h2>📋 下注记录</h2>
                <div id="recordsList" class="records-list">
                    <!-- 下注记录将在这里显示 -->
                </div>
            </section>
        </main>

        <!-- 中奖规则说明 -->
        <section class="rules-section">
            <h3>🏆 中奖规则说明</h3>
            <div class="rules-content">
                <p><strong>游戏规则：</strong>彩票由0-9的任意5位自然数排列而成，我们只取前面四位做为游戏规则！</p>
                <div class="rule-examples">
                    <div class="rule-item">
                        <h4>二字定 (赔率95)</h4>
                        <p>任意两个位置的号码匹配</p>
                        <p class="example">例如开奖1-2-3-4-5，中奖：1-2-X-X、1-X-3-X、X-2-3-X等</p>
                    </div>
                    <div class="rule-item">
                        <h4>三字定 (赔率950)</h4>
                        <p>任意三个位置的号码匹配</p>
                        <p class="example">例如开奖1-2-3-4-5，中奖：1-2-3-X、1-2-X-4、X-2-3-4等</p>
                    </div>
                    <div class="rule-item">
                        <h4>四字定 (赔率9500)</h4>
                        <p>四个位置的号码全部匹配</p>
                        <p class="example">例如开奖1-2-3-4-5，中奖：1-2-3-4</p>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 消息提示容器 -->
    <div id="messageContainer"></div>

    <script src="script.js"></script>
</body>
</html>
