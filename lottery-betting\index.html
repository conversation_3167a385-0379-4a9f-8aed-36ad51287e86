<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排列五积分下注网站</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎰</text></svg>">
</head>
<body>
    <div id="app">
        <header class="app-header">
            <h1>🎰 排列五积分下注</h1>
            <div class="points-display">
                当前积分: <span id="currentPoints">10000</span>
            </div>
        </header>

        <nav class="tab-nav">
            <button class="tab-button active" data-tab="results">
                <span class="tab-icon">🎯</span>
                <span class="tab-label">开奖结果</span>
            </button>
            <button class="tab-button" data-tab="selection">
                <span class="tab-icon">🎲</span>
                <span class="tab-label">选号区域</span>
            </button>
            <button class="tab-button" data-tab="user">
                <span class="tab-icon">👤</span>
                <span class="tab-label">用户中心</span>
            </button>
            <button class="tab-button" data-tab="records">
                <span class="tab-icon">📋</span>
                <span class="tab-label">下注记录</span>
            </button>
        </nav>

        <main class="app-main">
            <!-- 开奖结果模块 -->
            <div id="results-tab" class="tab-content active">
                <div class="lottery-results">
                    <div class="current-draw">
                        <h2>当前期号</h2>
                        <div class="period-info">
                            <span id="currentPeriod">等待开奖...</span>
                        </div>
                        <div class="draw-numbers">
                            <div class="number-display">
                                <span class="position-label">万位</span>
                                <span class="number" id="num1">-</span>
                            </div>
                            <div class="number-display">
                                <span class="position-label">仟位</span>
                                <span class="number" id="num2">-</span>
                            </div>
                            <div class="number-display">
                                <span class="position-label">佰位</span>
                                <span class="number" id="num3">-</span>
                            </div>
                            <div class="number-display">
                                <span class="position-label">十位</span>
                                <span class="number" id="num4">-</span>
                            </div>
                            <div class="number-display">
                                <span class="position-label">个位</span>
                                <span class="number" id="num5">-</span>
                            </div>
                        </div>
                        <div class="draw-actions">
                            <button id="drawBtn" class="draw-button">开奖</button>
                            <button id="fetchBtn" class="fetch-button">采集开奖</button>
                        </div>
                    </div>

                    <div class="draw-history">
                        <h3>开奖历史</h3>
                        <div id="historyList" class="history-list">
                            <!-- 历史记录将在这里显示 -->
                        </div>
                    </div>

                    <div class="winning-rules">
                        <h3>中奖规则</h3>
                        <div class="rules-content">
                            <p>彩票由0123456789的任意5位自然数排列而成，我们只取前面四位做为游戏规则！</p>
                            <div class="rule-item">
                                <h4>二字定 (赔率95)</h4>
                                <p>任意两个位置的号码匹配</p>
                                <p>例如：1-2-X-X、1-X-3-X、X-2-3-X等</p>
                            </div>
                            <div class="rule-item">
                                <h4>三字定 (赔率950)</h4>
                                <p>任意三个位置的号码匹配</p>
                                <p>例如：1-2-3-X、1-2-X-4、X-2-3-4等</p>
                            </div>
                            <div class="rule-item">
                                <h4>四字定 (赔率9500)</h4>
                                <p>四个位置的号码全部匹配</p>
                                <p>例如：1-2-3-4</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 选号区域模块 -->
            <div id="selection-tab" class="tab-content">
                <div class="number-selection">
                    <h2>选号区域</h2>
                    <div class="selection-grid">
                        <div class="position-selection">
                            <h3>万位</h3>
                            <div class="number-buttons" data-position="0">
                                <!-- 0-9数字按钮将在这里生成 -->
                            </div>
                            <div class="selected-numbers" id="selected-0"></div>
                        </div>
                        <div class="position-selection">
                            <h3>仟位</h3>
                            <div class="number-buttons" data-position="1">
                                <!-- 0-9数字按钮将在这里生成 -->
                            </div>
                            <div class="selected-numbers" id="selected-1"></div>
                        </div>
                        <div class="position-selection">
                            <h3>佰位</h3>
                            <div class="number-buttons" data-position="2">
                                <!-- 0-9数字按钮将在这里生成 -->
                            </div>
                            <div class="selected-numbers" id="selected-2"></div>
                        </div>
                        <div class="position-selection">
                            <h3>十位</h3>
                            <div class="number-buttons" data-position="3">
                                <!-- 0-9数字按钮将在这里生成 -->
                            </div>
                            <div class="selected-numbers" id="selected-3"></div>
                        </div>
                    </div>

                    <div class="bet-info">
                        <div class="bet-type">
                            <span>投注类型：</span>
                            <span id="betType">请选择号码</span>
                        </div>
                        <div class="bet-count">
                            <span>注数：</span>
                            <span id="betCount">0</span>
                        </div>
                        <div class="bet-odds">
                            <span>赔率：</span>
                            <span id="betOdds">-</span>
                        </div>
                    </div>

                    <div class="bet-amount">
                        <label for="betPoints">投注积分：</label>
                        <input type="number" id="betPoints" min="1" value="100">
                        <span>总投注：<span id="totalBet">0</span></span>
                    </div>

                    <div class="bet-actions">
                        <button id="clearSelection" class="clear-button">清空选择</button>
                        <button id="placeBet" class="bet-button" disabled>下注</button>
                    </div>
                </div>
            </div>

            <!-- 用户中心模块 -->
            <div id="user-tab" class="tab-content">
                <div class="user-center">
                    <h2>用户中心</h2>
                    <div class="user-stats">
                        <div class="stat-item">
                            <span class="stat-label">当前积分</span>
                            <span class="stat-value" id="userCurrentPoints">10000</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">总下注</span>
                            <span class="stat-value" id="userTotalBets">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">总奖金</span>
                            <span class="stat-value" id="userTotalWinnings">0</span>
                        </div>
                    </div>

                    <div class="user-actions">
                        <div class="action-group">
                            <h3>充值积分</h3>
                            <input type="number" id="rechargeAmount" min="1" value="1000" placeholder="充值金额">
                            <button id="rechargeBtn" class="action-button">充值</button>
                        </div>
                        <div class="action-group">
                            <h3>重置积分</h3>
                            <p>将积分重置为10000</p>
                            <button id="resetPointsBtn" class="action-button danger">重置积分</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 下注记录模块 -->
            <div id="records-tab" class="tab-content">
                <div class="betting-records">
                    <h2>下注记录</h2>
                    <div id="recordsList" class="records-list">
                        <!-- 下注记录将在这里显示 -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
