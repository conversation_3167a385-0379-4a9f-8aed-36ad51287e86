* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

#app {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

/* Header */
.app-header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.app-header h1 {
    font-size: 2rem;
    font-weight: bold;
}

.points-display {
    font-size: 1.2rem;
    background: rgba(255,255,255,0.2);
    padding: 10px 20px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.points-display .points,
#currentPoints {
    font-weight: bold;
    color: #ffd700;
}

/* Navigation */
.tab-nav {
    display: flex;
    background: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
}

.tab-button {
    flex: 1;
    padding: 15px 10px;
    border: none;
    background: transparent;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background: #e9ecef;
}

.tab-button.active {
    background: white;
    border-bottom-color: #ff6b6b;
    color: #ff6b6b;
}

.tab-icon {
    font-size: 1.5rem;
}

.tab-label {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Main Content */
.app-main {
    padding: 20px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Lottery Results */
.lottery-results {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.current-draw {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 2px solid #f1f3f4;
}

.current-draw h2 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
    font-size: 1.5rem;
}

.period-info {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.2rem;
    color: #666;
}

.draw-numbers {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.number-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.position-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.number {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.draw-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.draw-button, .fetch-button {
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.draw-button {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.fetch-button {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
}

.draw-button:hover, .fetch-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.draw-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* Draw History */
.draw-history {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 2px solid #f1f3f4;
}

.draw-history h3 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #ff6b6b;
    padding-bottom: 10px;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
    transition: background 0.3s ease;
}

.history-item:hover {
    background: #f8f9fa;
}

.history-item:last-child {
    border-bottom: none;
}

.history-period {
    font-weight: bold;
    color: #333;
}

.history-numbers {
    display: flex;
    gap: 8px;
}

.history-number {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: #ff6b6b;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

/* Winning Rules */
.winning-rules {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 2px solid #f1f3f4;
}

.winning-rules h3 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #ff6b6b;
    padding-bottom: 10px;
}

.rules-content p {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #666;
}

.rule-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
    border-left: 4px solid #ff6b6b;
}

.rule-item h4 {
    color: #ff6b6b;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.rule-item p {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

/* Number Selection */
.number-selection {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 2px solid #f1f3f4;
}

.number-selection h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
}

.selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.position-selection {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.position-selection h3 {
    text-align: center;
    margin-bottom: 15px;
    color: #333;
    font-size: 1.1rem;
}

.number-buttons {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    margin-bottom: 15px;
}

.number-btn {
    width: 40px;
    height: 40px;
    border: 2px solid #ddd;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.number-btn:hover {
    border-color: #ff6b6b;
    background: #fff5f5;
}

.number-btn.selected {
    background: #ff6b6b;
    color: white;
    border-color: #ff6b6b;
}

.selected-numbers {
    min-height: 25px;
    padding: 8px;
    background: white;
    border-radius: 5px;
    border: 1px solid #ddd;
    font-size: 0.9rem;
    color: #666;
}

/* Bet Info */
.bet-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.bet-info > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.bet-info span:first-child {
    font-weight: 500;
    color: #666;
}

.bet-info span:last-child {
    font-weight: bold;
    color: #333;
}

/* Bet Amount */
.bet-amount {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    flex-wrap: wrap;
}

.bet-amount label {
    font-weight: 500;
    color: #666;
}

.bet-amount input {
    padding: 10px 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    width: 150px;
}

.bet-amount input:focus {
    outline: none;
    border-color: #ff6b6b;
}

/* Bet Actions */
.bet-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.clear-button, .bet-button {
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-button {
    background: #6c757d;
    color: white;
}

.bet-button {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.bet-button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.clear-button:hover, .bet-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* User Center */
.user-center {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 2px solid #f1f3f4;
}

.user-center h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
}

.user-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-item {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    margin-bottom: 10px;
    opacity: 0.9;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: bold;
}

.user-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.action-group {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 15px;
    border: 2px solid #e9ecef;
}

.action-group h3 {
    margin-bottom: 15px;
    color: #333;
    border-bottom: 2px solid #ff6b6b;
    padding-bottom: 8px;
}

.action-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    margin-bottom: 15px;
}

.action-group input:focus {
    outline: none;
    border-color: #ff6b6b;
}

.action-button {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.action-button.danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Betting Records */
.betting-records {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 2px solid #f1f3f4;
}

.betting-records h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
}

.records-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.record-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.record-item:hover {
    border-color: #ff6b6b;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.record-period {
    font-weight: bold;
    color: #333;
    font-size: 1.1rem;
}

.record-status {
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: bold;
}

.record-status.pending {
    background: #ffc107;
    color: #856404;
}

.record-status.win {
    background: #28a745;
    color: white;
}

.record-status.lose {
    background: #dc3545;
    color: white;
}

.record-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.record-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.record-detail span:first-child {
    color: #666;
    font-weight: 500;
}

.record-detail span:last-child {
    color: #333;
    font-weight: bold;
}

.record-numbers {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.record-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #ff6b6b;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
}

.record-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.cancel-button {
    padding: 8px 20px;
    border: none;
    border-radius: 20px;
    background: #dc3545;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cancel-button:hover {
    background: #c82333;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .app-header h1 {
        font-size: 1.5rem;
    }
    
    .tab-nav {
        flex-wrap: wrap;
    }
    
    .tab-button {
        min-width: 25%;
    }
    
    .draw-numbers {
        gap: 10px;
    }
    
    .number {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .selection-grid {
        grid-template-columns: 1fr;
    }
    
    .bet-info {
        grid-template-columns: 1fr;
    }
    
    .bet-amount {
        flex-direction: column;
        align-items: stretch;
    }
    
    .bet-amount input {
        width: 100%;
    }
    
    .bet-actions {
        flex-direction: column;
    }
    
    .user-stats {
        grid-template-columns: 1fr;
    }
    
    .user-actions {
        grid-template-columns: 1fr;
    }
    
    .record-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .record-details {
        grid-template-columns: 1fr;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #ff6b6b;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.message.success {
    background: #28a745;
}

.message.error {
    background: #dc3545;
}

.message.warning {
    background: #ffc107;
    color: #856404;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
