* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 2rem;
    font-weight: bold;
}

.points-display {
    font-size: 1.2rem;
    background: rgba(255,255,255,0.2);
    padding: 10px 20px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.points-display span {
    font-weight: bold;
    color: #ffd700;
}

/* 主要内容区域 */
.main-content {
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* 模块通用样式 */
.module {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 2px solid #f1f3f4;
}

.module h2 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #ff6b6b;
    padding-bottom: 10px;
    font-size: 1.3rem;
}

/* 开奖结果模块 */
.lottery-results {
    grid-column: 1 / 3;
}

.draw-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.current-draw {
    text-align: center;
}

.period-info {
    margin-bottom: 20px;
    font-size: 1.1rem;
    color: #666;
}

.draw-numbers {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.number-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.position-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.number {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.draw-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* 开奖历史 */
.draw-history h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.1rem;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
    font-size: 0.9rem;
}

.history-period {
    font-weight: bold;
    color: #333;
}

.history-numbers {
    display: flex;
    gap: 5px;
}

.history-number {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    background: #ff6b6b;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
}

/* 选号区域模块 */
.input-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 25px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group label {
    font-weight: bold;
    color: #333;
    font-size: 1rem;
}

.number-input {
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.number-input:focus {
    outline: none;
    border-color: #ff6b6b;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.number-input.error {
    border-color: #dc3545;
    background-color: #fff5f5;
}

.help-text {
    color: #666;
    font-size: 0.8rem;
}

.error-text {
    color: #dc3545;
    font-size: 0.8rem;
    font-weight: bold;
}

/* 投注信息 */
.bet-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.bet-details {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.detail-item span:first-child {
    font-weight: 500;
    color: #666;
}

.detail-item span:last-child {
    font-weight: bold;
    color: #333;
}

/* 投注金额 */
.bet-amount {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    flex-wrap: wrap;
}

.bet-amount label {
    font-weight: 500;
    color: #666;
}

.points-input {
    padding: 10px 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    width: 150px;
}

.points-input:focus {
    outline: none;
    border-color: #ff6b6b;
}

.total-bet {
    font-weight: bold;
    color: #333;
}

/* 按钮样式 */
.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn:disabled {
    background: #ccc !important;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.bet-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* 用户中心模块 */
.user-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 25px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.stat-label {
    font-size: 0.9rem;
    margin-bottom: 8px;
    opacity: 0.9;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
}

.user-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.action-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 下注记录模块 */
.betting-records {
    grid-column: 1 / 3;
}

.records-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.record-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.record-item:hover {
    border-color: #ff6b6b;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.record-period {
    font-weight: bold;
    color: #333;
    font-size: 1.1rem;
}

.record-status {
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: bold;
}

.record-status.pending {
    background: #ffc107;
    color: #856404;
}

.record-status.win {
    background: #28a745;
    color: white;
}

.record-status.lose {
    background: #dc3545;
    color: white;
}

.record-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.record-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.record-detail span:first-child {
    color: #666;
    font-weight: 500;
}

.record-detail span:last-child {
    color: #333;
    font-weight: bold;
}

.record-numbers {
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #666;
}

.record-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.cancel-button {
    padding: 8px 20px;
    border: none;
    border-radius: 20px;
    background: #dc3545;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cancel-button:hover {
    background: #c82333;
    transform: translateY(-1px);
}

/* 规则说明 */
.rules-section {
    background: white;
    margin: 20px;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 2px solid #f1f3f4;
}

.rules-section h3 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #ff6b6b;
    padding-bottom: 10px;
}

.rules-content p {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #666;
}

.rule-examples {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.rule-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid #ff6b6b;
}

.rule-item h4 {
    color: #ff6b6b;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.rule-item p {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.example {
    font-style: italic;
    color: #666;
}

/* 消息提示 */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 1000;
    animation: slideIn 0.3s ease;
    max-width: 400px;
}

.message.success {
    background: #28a745;
}

.message.error {
    background: #dc3545;
}

.message.warning {
    background: #ffc107;
    color: #856404;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #ff6b6b;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
    }
    
    .lottery-results,
    .betting-records {
        grid-column: 1;
    }
    
    .draw-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .input-grid {
        grid-template-columns: 1fr;
    }
    
    .bet-details {
        grid-template-columns: 1fr;
    }
    
    .user-stats {
        grid-template-columns: 1fr;
    }
    
    .user-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .bet-amount {
        flex-direction: column;
        align-items: stretch;
    }
    
    .bet-amount .points-input {
        width: 100%;
    }
    
    .bet-actions {
        flex-direction: column;
    }
    
    .rule-examples {
        grid-template-columns: 1fr;
    }
}
