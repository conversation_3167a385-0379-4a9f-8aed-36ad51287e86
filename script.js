// 排列五积分下注游戏类
class LotteryGame {
    constructor() {
        this.data = this.loadData();
        this.selectedNumbers = [[], [], [], []]; // 万位、仟位、佰位、十位
        this.init();
    }

    // 从localStorage加载数据
    loadData() {
        const defaultData = {
            currentPeriod: '',
            currentNumbers: ['-', '-', '-', '-', '-'],
            lotteryHistory: [],
            userStats: {
                currentPoints: 10000,
                totalBets: 0,
                totalWinnings: 0
            },
            bettingRecords: [],
            isDrawn: false,
            lastFetchedPeriod: ''
        };

        const savedData = localStorage.getItem('lotteryGameData');
        return savedData ? { ...defaultData, ...JSON.parse(savedData) } : defaultData;
    }

    // 保存数据到localStorage
    saveData() {
        localStorage.setItem('lotteryGameData', JSON.stringify(this.data));
    }

    // 初始化
    init() {
        this.initEventListeners();
        this.updateDisplay();
    }

    // 初始化事件监听器
    initEventListeners() {
        // 采集开奖按钮
        document.getElementById('fetchBtn').addEventListener('click', () => {
            this.fetchLotteryResults();
        });

        // 新开奖按钮
        document.getElementById('newDrawBtn').addEventListener('click', () => {
            this.startNewDraw();
        });

        // 测试采集按钮
        document.getElementById('testBtn').addEventListener('click', () => {
            this.testFetch();
        });

        // 选号输入框事件
        const inputs = ['wan', 'qian', 'bai', 'shi'];
        inputs.forEach((id, index) => {
            const input = document.getElementById(id);
            input.addEventListener('input', (e) => {
                this.handleNumberInput(e, index);
            });
            input.addEventListener('blur', (e) => {
                this.validateInput(e, index);
            });
        });

        // 清空选择按钮
        document.getElementById('clearSelection').addEventListener('click', () => {
            this.clearSelection();
        });

        // 下注按钮
        document.getElementById('placeBet').addEventListener('click', () => {
            this.placeBet();
        });

        // 投注积分输入
        document.getElementById('betPoints').addEventListener('input', () => {
            this.updateBetInfo();
        });

        // 充值按钮
        document.getElementById('rechargeBtn').addEventListener('click', () => {
            this.rechargePoints();
        });

        // 重置积分按钮
        document.getElementById('resetPointsBtn').addEventListener('click', () => {
            this.resetPoints();
        });
    }

    // 处理数字输入
    handleNumberInput(event, position) {
        const input = event.target;
        let value = input.value;

        // 只允许输入0-9数字
        value = value.replace(/[^0-9]/g, '');
        
        // 移除重复数字
        const uniqueNumbers = [...new Set(value.split(''))];
        value = uniqueNumbers.join('');

        // 限制最多10个数字
        if (value.length > 10) {
            value = value.substring(0, 10);
        }

        input.value = value;
        
        // 更新选中的数字
        this.selectedNumbers[position] = value.split('').map(num => parseInt(num));
        
        // 移除错误样式
        input.classList.remove('error');
        this.removeErrorText(input);
        
        // 更新投注信息
        this.updateBetInfo();
    }

    // 验证输入
    validateInput(event, position) {
        const input = event.target;
        const value = input.value;

        if (value && !/^[0-9]+$/.test(value)) {
            input.classList.add('error');
            this.showErrorText(input, '只能输入0-9数字');
            return false;
        }

        // 检查是否有重复数字
        const numbers = value.split('');
        const uniqueNumbers = [...new Set(numbers)];
        if (numbers.length !== uniqueNumbers.length) {
            input.classList.add('error');
            this.showErrorText(input, '数字不能重复');
            return false;
        }

        return true;
    }

    // 显示错误文本
    showErrorText(input, message) {
        this.removeErrorText(input);
        const errorSpan = document.createElement('span');
        errorSpan.className = 'error-text';
        errorSpan.textContent = message;
        input.parentNode.appendChild(errorSpan);
    }

    // 移除错误文本
    removeErrorText(input) {
        const errorSpan = input.parentNode.querySelector('.error-text');
        if (errorSpan) {
            errorSpan.remove();
        }
    }

    // 更新投注信息
    updateBetInfo() {
        const selectedPositions = this.selectedNumbers.filter(pos => pos.length > 0);
        const betPointsInput = document.getElementById('betPoints');
        const betPoints = parseInt(betPointsInput.value) || 0;

        let betType = '请选择号码';
        let betCount = 0;
        let betOdds = '-';
        let totalBet = 0;

        if (selectedPositions.length >= 2) {
            if (selectedPositions.length === 2) {
                betType = '二字定';
                betOdds = '95';
            } else if (selectedPositions.length === 3) {
                betType = '三字定';
                betOdds = '950';
            } else if (selectedPositions.length === 4) {
                betType = '四字定';
                betOdds = '9500';
            }

            // 计算注数（各位置选中数字数量的乘积）
            betCount = selectedPositions.reduce((acc, pos) => acc * pos.length, 1);
            totalBet = betCount * betPoints;
        }

        document.getElementById('betType').textContent = betType;
        document.getElementById('betCount').textContent = betCount;
        document.getElementById('betOdds').textContent = betOdds;
        document.getElementById('totalBet').textContent = this.formatNumber(totalBet);

        // 更新下注按钮状态
        const placeBetBtn = document.getElementById('placeBet');
        placeBetBtn.disabled = selectedPositions.length < 2 || betPoints <= 0 || totalBet > this.data.userStats.currentPoints;
    }

    // 清空选择
    clearSelection() {
        this.selectedNumbers = [[], [], [], []];
        
        // 清空输入框
        const inputs = ['wan', 'qian', 'bai', 'shi'];
        inputs.forEach(id => {
            const input = document.getElementById(id);
            input.value = '';
            input.classList.remove('error');
            this.removeErrorText(input);
        });

        this.updateBetInfo();
    }

    // 下注
    placeBet() {
        const selectedPositions = this.selectedNumbers.filter(pos => pos.length > 0);
        if (selectedPositions.length < 2) {
            this.showMessage('请至少选择两个位置的号码', 'error');
            return;
        }

        // 验证所有输入
        const inputs = ['wan', 'qian', 'bai', 'shi'];
        let hasError = false;
        inputs.forEach((id, index) => {
            const input = document.getElementById(id);
            if (input.value && !this.validateInput({ target: input }, index)) {
                hasError = true;
            }
        });

        if (hasError) {
            this.showMessage('请检查输入的数字格式', 'error');
            return;
        }

        const betPoints = parseInt(document.getElementById('betPoints').value) || 0;
        const betCount = selectedPositions.reduce((acc, pos) => acc * pos.length, 1);
        const totalBet = betCount * betPoints;

        if (totalBet > this.data.userStats.currentPoints) {
            this.showMessage('积分不足', 'error');
            return;
        }

        // 创建下注记录
        const betRecord = {
            id: Date.now(),
            period: this.data.currentPeriod || '待开奖',
            selectedNumbers: [...this.selectedNumbers],
            betType: selectedPositions.length === 2 ? '二字定' : 
                    selectedPositions.length === 3 ? '三字定' : '四字定',
            betCount: betCount,
            betPoints: betPoints,
            totalBet: totalBet,
            odds: selectedPositions.length === 2 ? 95 : 
                  selectedPositions.length === 3 ? 950 : 9500,
            status: 'pending',
            winnings: 0,
            timestamp: new Date().toLocaleString()
        };

        // 扣除积分
        this.data.userStats.currentPoints -= totalBet;
        this.data.userStats.totalBets += totalBet;

        // 添加下注记录
        this.data.bettingRecords.unshift(betRecord);

        // 清空选择
        this.clearSelection();

        // 保存数据并更新显示
        this.saveData();
        this.updateDisplay();

        this.showMessage(`下注成功！投注${this.formatNumber(totalBet)}积分`, 'success');
    }

    // 开始新一期开奖
    startNewDraw() {
        if (confirm('确定要开始新一期开奖吗？这将重置当前开奖状态。')) {
            // 重置开奖状态
            this.data.isDrawn = false;
            this.data.currentPeriod = '';
            this.data.currentNumbers = ['-', '-', '-', '-', '-'];
            
            // 保存数据并更新显示
            this.saveData();
            this.updateDisplay();
            
            this.showMessage('新一期开奖已开始，可以进行投注！', 'success');
        }
    }

    // 采集开奖结果
    async fetchLotteryResults() {
        try {
            this.showMessage('正在采集开奖结果...', 'warning');

            // 添加加载状态
            const fetchBtn = document.getElementById('fetchBtn');
            const originalText = fetchBtn.textContent;
            fetchBtn.innerHTML = '<span class="loading"></span> 采集中...';
            fetchBtn.disabled = true;

            // 尝试获取数据
            let data = null;
            let dataSource = '';

            try {
                const response = await fetch('https://api.api68.com/QuanGuoCai/getLotteryInfo.do?lotCode=10044', {
                    method: 'GET',
                    mode: 'cors'
                });

                if (response.ok) {
                    data = await response.json();
                    dataSource = '官方API';
                    console.log('官方API数据:', data);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (corsError) {
                console.log('直接API访问失败:', corsError.message);
                this.showMessage('API访问失败，使用模拟数据...', 'warning');

                // 使用模拟数据
                data = this.generateMockLotteryData();
                dataSource = '模拟数据';
                console.log('使用模拟数据:', data);
            }

            // 验证数据格式
            console.log('验证数据:', data);

            if (data && data.result && Array.isArray(data.result) && data.result.length > 0) {
                const latestResult = data.result[0];
                console.log('最新结果:', latestResult);

                // 验证必要字段
                if (!latestResult.expect || !latestResult.opencode) {
                    console.error('数据格式错误:', latestResult);
                    this.showMessage('数据格式错误，请稍后重试', 'error');
                    return;
                }

                const period = latestResult.expect;
                const numbers = latestResult.opencode.split(',').map(num => parseInt(num));

                console.log('期号:', period, '号码:', numbers);

                // 检查是否是最新期号，防重复采集
                if (period === this.data.lastFetchedPeriod) {
                    this.showMessage('已经是最新期号，无需重复采集', 'warning');
                    return;
                }

                // 如果已经开奖，提示用户
                if (this.data.isDrawn && this.data.currentPeriod && this.data.currentPeriod !== '') {
                    this.showMessage('当前期已开奖，请先开始新一期', 'warning');
                    return;
                }

                this.data.currentPeriod = period;
                this.data.currentNumbers = numbers;
                this.data.isDrawn = true;
                this.data.lastFetchedPeriod = period;

                // 添加到历史记录
                this.data.lotteryHistory.unshift({
                    period: period,
                    numbers: [...numbers],
                    timestamp: new Date().toLocaleString()
                });

                // 只保留最近两期历史
                if (this.data.lotteryHistory.length > 2) {
                    this.data.lotteryHistory = this.data.lotteryHistory.slice(0, 2);
                }

                // 自动触发结算
                this.settleBets();

                // 保存数据并更新显示
                this.saveData();
                this.updateDisplay();

                this.showMessage(`采集开奖结果成功！(数据来源: ${dataSource})`, 'success');
            } else {
                console.error('数据验证失败:', data);
                this.showMessage('数据格式不正确，请稍后重试', 'error');
            }
        } catch (error) {
            console.error('采集开奖结果失败:', error);

            // 最后的备用方案：直接使用模拟数据
            try {
                console.log('使用最终备用方案：模拟数据');
                const mockData = this.generateMockLotteryData();

                if (mockData && mockData.result && mockData.result.length > 0) {
                    const latestResult = mockData.result[0];
                    const period = latestResult.expect;
                    const numbers = latestResult.opencode.split(',').map(num => parseInt(num));

                    // 如果已经开奖，提示用户
                    if (this.data.isDrawn && this.data.currentPeriod && this.data.currentPeriod !== '') {
                        this.showMessage('当前期已开奖，请先开始新一期', 'warning');
                        return;
                    }

                    this.data.currentPeriod = period;
                    this.data.currentNumbers = numbers;
                    this.data.isDrawn = true;
                    this.data.lastFetchedPeriod = period;

                    // 添加到历史记录
                    this.data.lotteryHistory.unshift({
                        period: period,
                        numbers: [...numbers],
                        timestamp: new Date().toLocaleString()
                    });

                    // 只保留最近两期历史
                    if (this.data.lotteryHistory.length > 2) {
                        this.data.lotteryHistory = this.data.lotteryHistory.slice(0, 2);
                    }

                    // 自动触发结算
                    this.settleBets();

                    // 保存数据并更新显示
                    this.saveData();
                    this.updateDisplay();

                    this.showMessage('采集开奖结果成功！(数据来源: 备用模拟数据)', 'success');
                } else {
                    this.showMessage('系统错误，请刷新页面重试', 'error');
                }
            } catch (backupError) {
                console.error('备用方案也失败:', backupError);
                this.showMessage('系统错误，请刷新页面重试', 'error');
            }
        } finally {
            // 恢复按钮状态
            const fetchBtn = document.getElementById('fetchBtn');
            fetchBtn.textContent = '采集开奖';
            fetchBtn.disabled = false;
        }
    }

    // 生成模拟开奖数据
    generateMockLotteryData() {
        const now = new Date();
        const dateStr = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`;
        const periodNum = String(Math.floor(Math.random() * 84) + 1).padStart(3, '0');
        const period = dateStr + periodNum;

        // 生成5位随机数字
        const numbers = [];
        for (let i = 0; i < 5; i++) {
            numbers.push(Math.floor(Math.random() * 10));
        }

        const mockData = {
            result: [{
                expect: period,
                opencode: numbers.join(','),
                opentime: now.toISOString()
            }]
        };

        console.log(`生成模拟开奖数据:`, mockData);

        return mockData;
    }

    // 测试采集功能
    testFetch() {
        console.log('=== 开始测试采集功能 ===');

        // 显示当前状态
        console.log('当前游戏状态:', {
            isDrawn: this.data.isDrawn,
            currentPeriod: this.data.currentPeriod,
            lastFetchedPeriod: this.data.lastFetchedPeriod
        });

        // 强制使用模拟数据进行测试
        try {
            const mockData = this.generateMockLotteryData();
            console.log('生成的测试数据:', mockData);

            if (mockData && mockData.result && mockData.result.length > 0) {
                const latestResult = mockData.result[0];
                const period = latestResult.expect;
                const numbers = latestResult.opencode.split(',').map(num => parseInt(num));

                console.log('解析结果:', { period, numbers });

                // 重置状态以允许测试
                this.data.isDrawn = false;
                this.data.currentPeriod = '';

                this.data.currentPeriod = period;
                this.data.currentNumbers = numbers;
                this.data.isDrawn = true;
                this.data.lastFetchedPeriod = period;

                // 添加到历史记录
                this.data.lotteryHistory.unshift({
                    period: period,
                    numbers: [...numbers],
                    timestamp: new Date().toLocaleString()
                });

                // 只保留最近两期历史
                if (this.data.lotteryHistory.length > 2) {
                    this.data.lotteryHistory = this.data.lotteryHistory.slice(0, 2);
                }

                // 自动触发结算
                this.settleBets();

                // 保存数据并更新显示
                this.saveData();
                this.updateDisplay();

                this.showMessage('测试采集成功！使用模拟数据', 'success');
                console.log('=== 测试采集完成 ===');
            } else {
                console.error('测试数据格式错误');
                this.showMessage('测试失败：数据格式错误', 'error');
            }
        } catch (error) {
            console.error('测试采集失败:', error);
            this.showMessage('测试失败：' + error.message, 'error');
        }
    }

    // 结算下注
    settleBets() {
        const winningNumbers = this.data.currentNumbers.slice(0, 4); // 只取前4位
        let totalWinnings = 0;

        this.data.bettingRecords.forEach(record => {
            if (record.status === 'pending') {
                const isWin = this.checkWin(record.selectedNumbers, winningNumbers);

                if (isWin) {
                    record.status = 'win';
                    record.winnings = record.totalBet * record.odds;
                    this.data.userStats.currentPoints += record.winnings;
                    this.data.userStats.totalWinnings += record.winnings;
                    totalWinnings += record.winnings;
                } else {
                    record.status = 'lose';
                    record.winnings = 0;
                }
            }
        });

        if (totalWinnings > 0) {
            this.showMessage(`恭喜中奖！获得奖金${this.formatNumber(totalWinnings)}积分`, 'success');
        }
    }

    // 检查是否中奖 - 改进版中奖逻辑
    checkWin(selectedNumbers, winningNumbers) {
        const selectedPositions = [];

        // 找出有选择数字的位置
        selectedNumbers.forEach((numbers, position) => {
            if (numbers.length > 0) {
                selectedPositions.push(position);
            }
        });

        // 检查每个选中位置的数字是否匹配
        return selectedPositions.every(position => {
            return selectedNumbers[position].includes(winningNumbers[position]);
        });
    }

    // 撤销下注
    cancelBet(betId) {
        const recordIndex = this.data.bettingRecords.findIndex(record => record.id === betId);

        if (recordIndex > -1) {
            const record = this.data.bettingRecords[recordIndex];

            if (record.status === 'pending') {
                // 退还积分
                this.data.userStats.currentPoints += record.totalBet;
                this.data.userStats.totalBets -= record.totalBet;

                // 删除记录
                this.data.bettingRecords.splice(recordIndex, 1);

                // 保存数据并更新显示
                this.saveData();
                this.updateDisplay();

                this.showMessage('撤单成功！', 'success');
            } else {
                this.showMessage('该注单已开奖，无法撤销', 'error');
            }
        }
    }

    // 充值积分
    rechargePoints() {
        const amount = parseInt(document.getElementById('rechargeAmount').value) || 0;

        if (amount <= 0) {
            this.showMessage('请输入有效的充值金额', 'error');
            return;
        }

        this.data.userStats.currentPoints += amount;
        this.saveData();
        this.updateDisplay();

        this.showMessage(`充值成功！获得${this.formatNumber(amount)}积分`, 'success');
    }

    // 重置积分
    resetPoints() {
        if (confirm('确定要重置积分吗？这将清除所有数据！')) {
            this.data = {
                currentPeriod: '',
                currentNumbers: ['-', '-', '-', '-', '-'],
                lotteryHistory: [],
                userStats: {
                    currentPoints: 10000,
                    totalBets: 0,
                    totalWinnings: 0
                },
                bettingRecords: [],
                isDrawn: false,
                lastFetchedPeriod: ''
            };

            this.saveData();
            this.updateDisplay();

            this.showMessage('数据重置成功！', 'success');
        }
    }

    // 格式化数字显示
    formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }

    // 更新显示
    updateDisplay() {
        this.updateLotteryDisplay();
        this.updateUserStatsDisplay();
        this.updateBettingRecordsDisplay();
    }

    // 更新开奖显示
    updateLotteryDisplay() {
        // 更新当前期号
        document.getElementById('currentPeriod').textContent = this.data.currentPeriod || '等待开奖...';

        // 更新开奖号码
        for (let i = 0; i < 5; i++) {
            document.getElementById(`num${i + 1}`).textContent = this.data.currentNumbers[i];
        }

        // 更新历史记录
        this.updateHistoryDisplay();
    }

    // 更新历史记录显示
    updateHistoryDisplay() {
        const historyList = document.getElementById('historyList');
        historyList.innerHTML = '';

        if (this.data.lotteryHistory.length === 0) {
            historyList.innerHTML = '<p style="text-align: center; color: #666;">暂无开奖历史</p>';
            return;
        }

        this.data.lotteryHistory.forEach(history => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';

            historyItem.innerHTML = `
                <div class="history-period">${history.period}</div>
                <div class="history-numbers">
                    ${history.numbers.map(num => `<span class="history-number">${num}</span>`).join('')}
                </div>
            `;

            historyList.appendChild(historyItem);
        });
    }

    // 更新用户统计显示
    updateUserStatsDisplay() {
        document.getElementById('currentPoints').textContent = this.formatNumber(this.data.userStats.currentPoints);
        document.getElementById('userCurrentPoints').textContent = this.formatNumber(this.data.userStats.currentPoints);
        document.getElementById('userTotalBets').textContent = this.formatNumber(this.data.userStats.totalBets);
        document.getElementById('userTotalWinnings').textContent = this.formatNumber(this.data.userStats.totalWinnings);
    }

    // 更新下注记录显示
    updateBettingRecordsDisplay() {
        const recordsList = document.getElementById('recordsList');
        recordsList.innerHTML = '';

        if (this.data.bettingRecords.length === 0) {
            recordsList.innerHTML = '<p style="text-align: center; color: #666;">暂无下注记录</p>';
            return;
        }

        this.data.bettingRecords.forEach(record => {
            const recordItem = document.createElement('div');
            recordItem.className = 'record-item';

            const statusText = record.status === 'pending' ? '未开奖' :
                              record.status === 'win' ? '中奖' : '未中奖';
            const statusClass = record.status;

            // 格式化投注号码显示
            const selectedNumbersDisplay = record.selectedNumbers.map((numbers, index) => {
                if (numbers.length > 0) {
                    return `${['万', '仟', '佰', '十'][index]}位: ${numbers.join(',')}`;
                }
                return '';
            }).filter(str => str).join(' | ');

            // 显示开奖号码（如果已开奖）
            const drawNumbersDisplay = this.data.isDrawn && this.data.currentNumbers[0] !== '-' ?
                `开奖号码: ${this.data.currentNumbers.slice(0, 4).join('-')}` : '';

            recordItem.innerHTML = `
                <div class="record-header">
                    <div class="record-period">期号: ${record.period}</div>
                    <div class="record-status ${statusClass}">${statusText}</div>
                </div>
                <div class="record-details">
                    <div class="record-detail">
                        <span>投注类型:</span>
                        <span>${record.betType}</span>
                    </div>
                    <div class="record-detail">
                        <span>注数:</span>
                        <span>${record.betCount}</span>
                    </div>
                    <div class="record-detail">
                        <span>投注积分:</span>
                        <span>${this.formatNumber(record.totalBet)}</span>
                    </div>
                    <div class="record-detail">
                        <span>中奖奖金:</span>
                        <span>${this.formatNumber(record.winnings)}</span>
                    </div>
                </div>
                <div class="record-numbers">
                    <strong>投注号码:</strong> ${selectedNumbersDisplay}
                    ${drawNumbersDisplay ? `<br><strong>${drawNumbersDisplay}</strong>` : ''}
                </div>
                ${record.status === 'pending' ? `
                    <div class="record-actions">
                        <button class="cancel-button" onclick="game.cancelBet(${record.id})">撤单</button>
                    </div>
                ` : ''}
            `;

            recordsList.appendChild(recordItem);
        });
    }

    // 显示消息
    showMessage(text, type = 'success') {
        const message = document.createElement('div');
        message.className = `message ${type}`;
        message.textContent = text;

        const container = document.getElementById('messageContainer');
        container.appendChild(message);

        setTimeout(() => {
            message.remove();
        }, 3000);
    }
}

// 初始化游戏
let game;
document.addEventListener('DOMContentLoaded', () => {
    game = new LotteryGame();
});
