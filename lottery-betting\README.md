# 排列五积分下注网站

一个纯前端的排列五积分下注游戏网站，支持选号下注、开奖结算、积分管理等功能。

## 功能特性

### 🎯 开奖结果模块
- **手动开奖**: 生成随机开奖号码
- **API采集**: 从官方API采集真实开奖结果
- **开奖历史**: 保留最近两期开奖记录
- **中奖规则**: 详细的游戏规则说明

### 🎲 选号区域模块
- **四位选号**: 万位、仟位、佰位、十位选号
- **智能计算**: 自动计算注数和赔率
- **投注类型**:
  - 二字定: 任意两个位置匹配 (赔率95)
  - 三字定: 任意三个位置匹配 (赔率950)
  - 四字定: 四个位置全部匹配 (赔率9500)

### 👤 用户中心模块
- **积分管理**: 查看当前积分、总下注、总奖金
- **充值功能**: 手动充值积分
- **数据重置**: 重置所有游戏数据

### 📋 下注记录模块
- **记录查看**: 查看所有下注记录
- **撤单功能**: 未开奖的注单可以撤销
- **自动结算**: 开奖后自动计算中奖奖金

## 游戏规则

### 基本规则
- 彩票由0-9的任意5位自然数排列而成
- 游戏只使用前4位数字（万位、仟位、佰位、十位）
- 每个位置可以选择多个数字，但不能重复

### 中奖规则
假设开奖结果为：1-2-3-4-5

**二字定中奖示例**:
- 1-2-X-X (万位1，仟位2)
- 1-X-3-X (万位1，佰位3)
- X-2-X-4 (仟位2，十位4)
- 等等...

**三字定中奖示例**:
- 1-2-3-X (万位1，仟位2，佰位3)
- 1-2-X-4 (万位1，仟位2，十位4)
- 等等...

**四字定中奖示例**:
- 1-2-3-4 (四个位置全部匹配)

### 注数计算
注数 = 各选中位置数字数量的乘积

例如：
- 万位选择2个数字，仟位选择3个数字 = 2 × 3 = 6注
- 万位选择1个数字，仟位选择2个数字，佰位选择2个数字 = 1 × 2 × 2 = 4注

## 技术特性

- **纯前端**: 使用HTML、CSS、JavaScript开发
- **响应式设计**: 支持手机、平板、电脑等设备
- **数据持久化**: 使用localStorage保存游戏数据
- **实时API**: 支持从官方API采集开奖结果
- **用户友好**: 直观的界面设计和操作流程

## 使用方法

### 启动网站
1. 下载所有文件到本地目录
2. 使用任意HTTP服务器启动网站，例如：
   ```bash
   # 使用Python
   python -m http.server 8080
   
   # 使用Node.js
   npx http-server -p 8080
   ```
3. 在浏览器中访问 `http://localhost:8080`

### 游戏流程
1. **选号**: 在选号区域选择要投注的数字
2. **下注**: 设置投注积分并确认下注
3. **开奖**: 点击开奖按钮或采集官方开奖结果
4. **结算**: 系统自动计算中奖情况并发放奖金

### 注意事项
- 初始积分为10000分
- 只有未开奖的注单可以撤销
- 开奖历史只保留最近两期
- 所有数据保存在浏览器本地存储中

## 文件结构

```
lottery-betting/
├── index.html      # 主页面
├── styles.css      # 样式文件
├── script.js       # 功能脚本
└── README.md       # 说明文档
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发说明

这是一个纯前端项目，所有功能都在客户端实现。数据通过localStorage进行持久化存储，API采集功能可能受到CORS限制。

如需修改或扩展功能，可以直接编辑对应的HTML、CSS、JavaScript文件。

## 免责声明

本项目仅供学习和娱乐使用，不涉及真实货币交易。请遵守当地法律法规，理性游戏。
